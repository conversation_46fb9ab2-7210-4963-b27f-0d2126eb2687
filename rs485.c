#include "appconfig.h"
#include <sys/stat.h>


int uart_fd = 0;
char buf[400];
unsigned char segCount = 0;
unsigned char lastSegCount = 0;
unsigned long lastTickCount = 0;

unsigned long GetTickCount() {
struct timespec ts;
clock_gettime(CLOCK_MONOTONIC, &ts); // 获取单调时间
return (ts.tv_sec * 1000) + (ts.tv_nsec / 1000000);
}

void uart_sigio_handler(int sig) {

    int n = read(uart_fd, buf+32*segCount, sizeof(buf));

    if (32 == n)
    {
        //读入数据流，启动定时器检测和拼接下一帧
        segCount++;
        lastTickCount = GetTickCount();
        // 线程中判断32整数倍包长是否是最后一帧
    }
    else if (n > 0)
    {
        //最后一帧
        buf[n+32*segCount] = 0;
        uart_string_handler(buf, n+32*segCount);
        segCount = 0;
    }
}


void* uart_thread(void* arg) {

    chmod("/dev/ttyUSB0", 0777);

    int fd = open("/dev/ttyUSB0", O_RDWR | O_NOCTTY);
    if (fd < 0) {
      perror("No 485 Device avaible\n");
      exit(EXIT_FAILURE);
    }

    struct termios tty;
    tcgetattr(fd, &tty);
    cfsetispeed(&tty, B9600);  // 输入波特率9600
    cfsetospeed(&tty, B9600);  // 输出波特率9600
    tty.c_cflag &= ~PARENB;    // 无校验位
    tty.c_cflag &= ~CSTOPB;    // 1位停止位
    tty.c_cflag &= ~CSIZE;
    tty.c_cflag |= CS8;        // 8数据位
    tty.c_cc[VMIN] =64;
    tty.c_cc[VTIME]=200;
    tcsetattr(fd, TCSANOW, &tty);

    fcntl(fd, F_SETOWN, getpid());          // 绑定当前进程为信号接收者
    fcntl(fd, F_SETFL, O_ASYNC | O_NONBLOCK); // 启用异步通知
    signal(SIGIO, uart_sigio_handler);          // 注册信号处理函数

    uart_fd = fd;

    while(1) {
      usleep(50000);     //50ms 检查32整数倍的帧
      if (segCount > 0)
      {
          if (abs( lastTickCount - GetTickCount()) > 60 )
          {
              //超时最后一帧
              buf[32*segCount] = 0;
              uart_string_handler(buf, 32*segCount);
              segCount = 0;
          }
      }
      //异常处理
    }
    const char *msg = "Finish Serial\\n";
    write(fd, msg, strlen(msg));
    close(fd);
    return 0;
}

