# USB串口设备配置说明

## 当前状态
✅ Docker环境已配置完成  
✅ 程序编译成功  
❓ 需要配置USB串口设备映射  

## USB串口设备配置步骤

### 1. 检查USB设备连接
首先确保您的USB转串口设备已连接到Mac：

```bash
# 检查所有USB串口设备
ls -la /dev/cu.*
ls -la /dev/tty.*

# 或者使用系统信息
system_profiler SPUSBDataType | grep -i serial
```

### 2. 常见的USB转串口设备名称
在macOS上，USB转串口设备通常显示为：
- `/dev/cu.usbserial-*` (FTDI设备)
- `/dev/cu.usbmodem-*` (CDC ACM设备)
- `/dev/cu.SLAB_USBtoUART*` (CP210x设备)
- `/dev/cu.wchusbserial*` (CH340设备)

### 3. 运行带USB支持的容器

#### 方法1：自动检测设备
```bash
./run_with_usb.sh
```
这个脚本会：
- 自动检测可用的USB串口设备
- 将设备映射到容器内的 `/dev/ttyUSB0`
- 启动Linux容器

#### 方法2：手动指定设备
如果自动检测失败，可以手动运行：
```bash
# 替换 /dev/cu.usbserial-XXXX 为您的实际设备
docker run -it --rm \
    --privileged \
    --device=/dev/cu.usbserial-XXXX:/dev/ttyUSB0 \
    -v "$(pwd)":/app \
    access-controller /bin/bash
```

### 4. 在容器内测试设备
进入容器后，检查设备是否正确映射：
```bash
# 检查设备文件
ls -la /dev/ttyUSB*

# 检查设备权限
ls -la /dev/ttyUSB0

# 测试设备通信（可选）
echo "test" > /dev/ttyUSB0
```

### 5. 编译和运行程序
在容器内：
```bash
# 使用灵活串口检测版本编译
./build_and_run.sh

# 或者手动编译
cd build
cmake -DUSE_FLEXIBLE_SERIAL=ON ..
make
./testdemo
```

## 故障排除

### 问题1：找不到USB设备
**症状**：`ls /dev/cu.*` 没有输出
**解决方案**：
1. 检查USB设备是否正确连接
2. 安装USB转串口驱动程序：
   - CP210x: https://www.silabs.com/developers/usb-to-uart-bridge-vcp-drivers
   - FTDI: https://ftdichip.com/drivers/vcp-drivers/
   - CH340: 搜索"CH340 macOS driver"

### 问题2：设备权限问题
**症状**：`Permission denied` 错误
**解决方案**：
```bash
# 在主机上修改设备权限
sudo chmod 666 /dev/cu.usbserial-XXXX
```

### 问题3：设备被占用
**症状**：`Device busy` 错误
**解决方案**：
1. 关闭其他可能使用串口的程序
2. 重新插拔USB设备
3. 重启Docker Desktop

### 问题4：Docker设备映射失败
**症状**：容器内看不到 `/dev/ttyUSB0`
**解决方案**：
1. 确保使用 `--privileged` 参数
2. 检查设备路径是否正确
3. 尝试重启Docker Desktop

## 测试模式
如果暂时无法配置USB设备，程序会自动进入测试模式：
- 模拟USB GPIO设备
- 模拟串口通信
- 显示模拟的门禁操作

## 下一步
配置成功后，您可以：
1. 测试读卡器功能
2. 测试门锁控制
3. 监控串口通信数据
4. 根据实际硬件调整配置参数
