#include "appconfig.h"
#include <sys/stat.h>

int uart_fd = 0;
char buf[400];
unsigned char segCount = 0;
unsigned char lastSegCount = 0;
unsigned long lastTickCount = 0;

unsigned long GetTickCount() {
    struct timespec ts;
    clock_gettime(CLOCK_MONOTONIC, &ts);
    return (ts.tv_sec * 1000) + (ts.tv_nsec / 1000000);
}

void uart_sigio_handler(int sig) {
    int n = read(uart_fd, buf+32*segCount, sizeof(buf));

    if (32 == n) {
        segCount++;
        lastTickCount = GetTickCount();
    } else if (n > 0) {
        buf[n+32*segCount] = 0;
        uart_string_handler(buf, n+32*segCount);
        segCount = 0;
    }
}

// 尝试打开macOS串口设备
int try_open_macos_serial(const char* device_path) {
    printf("尝试打开macOS串口设备: %s\n", device_path);
    
    if (access(device_path, F_OK) != 0) {
        printf("设备文件不存在: %s\n", device_path);
        return -1;
    }
    
    int fd = open(device_path, O_RDWR | O_NOCTTY);
    if (fd < 0) {
        perror("无法打开设备");
        return -1;
    }
    
    printf("✅ 成功打开设备: %s (fd=%d)\n", device_path, fd);
    return fd;
}

void* uart_thread(void* arg) {
    const char* macos_serial_devices[] = {
        "/dev/cu.usbserial-1410",    // 您的USB设备
        "/dev/cu.usbserial-1420",    // 备用
        "/dev/cu.usbmodem1411",      // USB CDC设备
        "/dev/cu.usbmodem1421",      // 备用USB CDC设备
        "/dev/cu.SLAB_USBtoUART",    // CP210x设备
        "/dev/cu.wchusbserial1410",  // CH340设备
        NULL
    };
    
    int fd = -1;
    const char* used_device = NULL;
    
    printf("🔍 搜索macOS串口设备...\n");
    
    // 尝试打开各种可能的串口设备
    for (int i = 0; macos_serial_devices[i] != NULL; i++) {
        fd = try_open_macos_serial(macos_serial_devices[i]);
        if (fd >= 0) {
            used_device = macos_serial_devices[i];
            break;
        }
    }
    
    if (fd < 0) {
        printf("❌ 未找到可用的串口设备\n");
        printf("💡 请检查：\n");
        printf("   1. USB转串口设备是否已连接\n");
        printf("   2. 设备驱动是否已安装\n");
        printf("   3. 设备是否被其他程序占用\n");
        printf("\n");
        printf("🔍 当前可用的设备文件：\n");
        system("ls -la /dev/cu.* 2>/dev/null || echo '未找到cu设备'");
        
        // 进入测试模式
        printf("\n⚠️  进入测试模式（无硬件）\n");
        while(1) {
            sleep(10);
            printf("[测试模式] 串口线程运行中...\n");
            
            // 模拟接收数据
            static int counter = 0;
            counter++;
            if (counter % 6 == 0) {
                char test_data[64];
                snprintf(test_data, sizeof(test_data), "[模拟] 卡号: %08d\n", counter/6);
                uart_string_handler(test_data, strlen(test_data));
            }
        }
        return NULL;
    }

    printf("✅ 使用串口设备: %s\n", used_device);

    // 配置串口参数
    struct termios tty;
    tcgetattr(fd, &tty);
    cfsetispeed(&tty, B9600);
    cfsetospeed(&tty, B9600);
    tty.c_cflag &= ~PARENB;
    tty.c_cflag &= ~CSTOPB;
    tty.c_cflag &= ~CSIZE;
    tty.c_cflag |= CS8;
    tty.c_cc[VMIN] = 1;
    tty.c_cc[VTIME] = 10;
    tcsetattr(fd, TCSANOW, &tty);

    // 设置非阻塞模式
    fcntl(fd, F_SETFL, O_NONBLOCK);

    uart_fd = fd;
    
    printf("✅ 串口配置完成，开始监听数据...\n");
    printf("💡 您可以通过串口发送数据进行测试\n");

    while(1) {
        // 检查是否有数据可读
        char read_buf[256];
        int n = read(fd, read_buf, sizeof(read_buf)-1);
        
        if (n > 0) {
            read_buf[n] = 0;
            printf("📥 接收到数据: %s", read_buf);
            uart_string_handler(read_buf, n);
        }
        
        usleep(50000); // 50ms
        
        if (segCount > 0) {
            if (labs(lastTickCount - GetTickCount()) > 60) {
                buf[32*segCount] = 0;
                uart_string_handler(buf, 32*segCount);
                segCount = 0;
            }
        }
    }
    
    const char *msg = "Finish Serial\n";
    write(fd, msg, strlen(msg));
    close(fd);
    return NULL;
}
