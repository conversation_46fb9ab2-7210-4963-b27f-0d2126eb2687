#include "appconfig.h"

#include "io.h"
#include "usb_device.h"

#define LED_RED      14         // 锁控制信号
#define LED_GREEN    15         // 锁控制信号2
#define KEY_GREEN    7          //  报警输入
#define KEY_YELLOW   5          //  防拆信号
#define KEY_BLUE     6          //  手动开门

#define DOOR_UNLOCK_DELAY  5

timer_t timerid = NULL;
struct sigevent sev = {
        .sigev_notify = SIGEV_SIGNAL,
        .sigev_signo = SIGALRM
    };
struct itimerspec its;

void door_unlock_timer_handler();

int SerialNumber, ret = 0, i;

void* keyboard_thread(void* arg) {

    int SerialNumbers[16];
    int PinState = 0;

    // hardware 初始化
    ret = UsbDevice_Scan(SerialNumbers);
    if (ret < 0)
    {
        perror("UsbDevice_Scan");
        pthread_exit(NULL);
    }
    else if (ret == 0)
    {
        perror("No GPIO device\n");
        pthread_exit(NULL);
    }
    else
    {
        for (i = 0; i < ret; i++)
        {
            printf("Dev%d SN: %d\n", i, SerialNumbers[i]);
            //保存SerialNumber
            SerialNumber = SerialNumbers[0];
        }
    }

    //配置输入按键
    ret = IO_InitPin(SerialNumber, KEY_GREEN, 0, 1);
    if (ret < 0)
    {
        printf("Error: %d\n", ret);
        pthread_exit(NULL);
    }
    ret = IO_InitPin(SerialNumber, KEY_YELLOW, 0, 1);
    if (ret < 0)
    {
        printf("Error: %d\n", ret);
        pthread_exit(NULL);
    }
    ret = IO_InitPin(SerialNumber, KEY_BLUE, 0, 1);
    if (ret < 0)
    {
        printf("Error: %d\n", ret);
        pthread_exit(NULL);
    }

    //配置输出开关量
    ret = IO_InitPin(SerialNumber, LED_RED, 1, 0);
    if (ret < 0)
    {
        printf("Error: %d\n", ret);
        pthread_exit(NULL);
    }
    ret = IO_InitPin(SerialNumber, LED_GREEN, 1, 0);
    if (ret < 0)
    {
        printf("Error: %d\n", ret);
        pthread_exit(NULL);
    }

    // 默认关门
    ret = IO_WritePin(SerialNumber, LED_RED, 0);

    // 创建线程通知开锁定时器，指定处理句柄
    sev.sigev_notify = SIGEV_THREAD;
    sev.sigev_notify_function = door_unlock_timer_handler;
    sev.sigev_value.sival_int = 1;
    timer_create(CLOCK_REALTIME, &sev, &timerid);

    while (1) {
        //读取KEY_BLUE状态
        ret = IO_ReadPin(SerialNumber, KEY_BLUE, &PinState);
        if (ret < 0)
        {
          perror("IO_ReadPin");
          exit(EXIT_FAILURE);
        }
        if (0 == PinState)    //开锁按钮按下
        {
          //printf("Read BLUE KEY: %d", PinState);
          door_unlock();
          sleep(2);
        }
        usleep(20000);   //20ms检测按键
    }

    pthread_exit(NULL);
}

void door_unlock_timer_handler()
{
  // 延时DOOR_UNLOCK_DELAY结束关门
  // LED_RED 输出低电平
  ret = IO_WritePin(SerialNumber, LED_RED, 0);
  if (ret < 0)
  {
      perror("IO_WritePin");
      exit(EXIT_FAILURE);
  }
}

void door_unlock()
{
  //LED_RED 输出高电平，开门
  ret = IO_WritePin(SerialNumber, LED_RED, 1);
  if (ret < 0)
  {
      perror("IO_WritePin");
      exit(EXIT_FAILURE);
  }
  // 开门延时 再关门

  // 设置DOOR_UNLOCK_DELAY秒后触发单次定时器
  its.it_value.tv_sec = DOOR_UNLOCK_DELAY;
  its.it_value.tv_nsec = 0;
  its.it_interval.tv_sec = 0;
  its.it_interval.tv_nsec = 0;

  timer_settime(timerid, 0, &its, NULL);
}


int alarm_in()   // 1 输入报警  0无报警
{
    int PinState = 0;
    IO_ReadPin(SerialNumber, KEY_GREEN, &PinState);
    return PinState;
}
int button_in()   // 0 按键按下  1按键未按下
{
    int PinState = 0;
    IO_ReadPin(SerialNumber, KEY_BLUE, &PinState);
    return PinState;
}
extern int case_in()  // 1 防拆报警  0无报警
{
    int PinState = 0;
    IO_ReadPin(SerialNumber, KEY_YELLOW, &PinState);
    return PinState;
}
