{"artifacts": [{"path": "testdemo"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "install", "target_link_libraries"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 5, "parent": 0}, {"command": 1, "file": 0, "line": 13, "parent": 0}, {"command": 2, "file": 0, "line": 10, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -fdiagnostics-color=always"}], "language": "C", "sourceIndexes": [0, 2, 3]}], "id": "testdemo::@6890427a1f51a3e7e1df", "install": {"destinations": [{"backtrace": 2, "path": "bin"}], "prefix": {"path": "/tmp"}}, "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"fragment": "", "role": "flags"}, {"backtrace": 3, "fragment": "-lusb-1.0", "role": "libraries"}, {"backtrace": 3, "fragment": "-lrockmong", "role": "libraries"}], "language": "C"}, "name": "testdemo", "nameOnDisk": "testdemo", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0, 2, 3]}, {"name": "<PERSON><PERSON>", "sourceIndexes": [1]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "main.c", "sourceGroupIndex": 0}, {"backtrace": 1, "path": "appconfig.h", "sourceGroupIndex": 1}, {"backtrace": 1, "compileGroupIndex": 0, "path": "hardware.c", "sourceGroupIndex": 0}, {"backtrace": 1, "compileGroupIndex": 0, "path": "rs485.c", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}