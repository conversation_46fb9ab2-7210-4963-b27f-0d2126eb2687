{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "/home/<USER>/Qt/Tools/CMake/bin/cmake", "cpack": "/home/<USER>/Qt/Tools/CMake/bin/cpack", "ctest": "/home/<USER>/Qt/Tools/CMake/bin/ctest", "root": "/home/<USER>/Qt/Tools/CMake/share/cmake-3.30"}, "version": {"isDirty": false, "major": 3, "minor": 30, "patch": 5, "string": "3.30.5", "suffix": ""}}, "objects": [{"jsonFile": "codemodel-v2-6c5d3185d7c54340eef3.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "cache-v2-4ea0ec76c4ec7c3f251b.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-74588a0b32d438255aa1.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}], "reply": {"cache-v2": {"jsonFile": "cache-v2-4ea0ec76c4ec7c3f251b.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-74588a0b32d438255aa1.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, "codemodel-v2": {"jsonFile": "codemodel-v2-6c5d3185d7c54340eef3.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}}}