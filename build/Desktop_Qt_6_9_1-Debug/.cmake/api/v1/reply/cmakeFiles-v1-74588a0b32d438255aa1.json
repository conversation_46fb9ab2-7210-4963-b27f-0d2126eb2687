{"inputs": [{"path": "CMakeLists.txt"}, {"isGenerated": true, "path": "build/Desktop_Qt_6_9_1-Debug/.qtc/package-manager/auto-setup.cmake"}, {"isGenerated": true, "path": "build/Desktop_Qt_6_9_1-Debug/CMakeFiles/3.30.5/CMakeSystem.cmake"}, {"isGenerated": true, "path": "build/Desktop_Qt_6_9_1-Debug/.qtc/package-manager/maintenance_tool_provider.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/CMakeSystemSpecificInitialize.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/Platform/Linux-Initialize.cmake"}, {"isGenerated": true, "path": "build/Desktop_Qt_6_9_1-Debug/CMakeFiles/3.30.5/CMakeCCompiler.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/CMakeSystemSpecificInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/CMakeGenericSystem.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/CMakeInitializeConfigs.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/Platform/Linux.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/Platform/UnixPaths.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/CMakeCInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/CMakeLanguageInformation.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/Compiler/GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/Compiler/GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/Compiler/CMakeCommonCompilerMacros.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/Platform/Linux-GNU-C.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/Platform/Linux-GNU.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/CMakeCommonLanguageInclude.cmake"}, {"isCMake": true, "isExternal": true, "path": "/home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/GNUInstallDirs.cmake"}], "kind": "cmakeFiles", "paths": {"build": "/home/<USER>/testdemo/build/Desktop_Qt_6_9_1-Debug", "source": "/home/<USER>/testdemo"}, "version": {"major": 1, "minor": 1}}