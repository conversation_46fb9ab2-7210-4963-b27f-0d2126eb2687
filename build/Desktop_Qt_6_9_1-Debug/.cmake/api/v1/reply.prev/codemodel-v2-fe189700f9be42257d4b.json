{"configurations": [{"directories": [{"build": ".", "hasInstallRule": true, "jsonFile": "directory-.-Debug-fbfe7a22fb33e3e32e61.json", "minimumCMakeVersion": {"string": "3.16"}, "projectIndex": 0, "source": ".", "targetIndexes": [0]}], "name": "Debug", "projects": [{"directoryIndexes": [0], "name": "testdemo", "targetIndexes": [0]}], "targets": [{"directoryIndex": 0, "id": "testdemo::@6890427a1f51a3e7e1df", "jsonFile": "target-testdemo-Debug-99e41b41881ee7bc8e6a.json", "name": "testdemo", "projectIndex": 0}]}], "kind": "codemodel", "paths": {"build": "/home/<USER>/testdemo/build/Desktop_Qt_6_9_1-Debug", "source": "/home/<USER>/testdemo"}, "version": {"major": 2, "minor": 7}}