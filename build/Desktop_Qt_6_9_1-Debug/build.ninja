# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.30

# This file contains all the build statements describing the
# compilation DAG.

# =============================================================================
# Write statements declared in CMakeLists.txt:
# 
# Which is the root file.
# =============================================================================

# =============================================================================
# Project: testdemo
# Configurations: Debug
# =============================================================================

#############################################
# Minimal version of Ninja required by this file

ninja_required_version = 1.5


#############################################
# Set configuration variable for custom commands.

CONFIGURATION = Debug
# =============================================================================
# Include auxiliary files.


#############################################
# Include rules file.

include CMakeFiles/rules.ninja

# =============================================================================

#############################################
# Logical path to working directory; prefix for absolute paths.

cmake_ninja_workdir = /home/<USER>/testdemo/build/Desktop_Qt_6_9_1-Debug/
# =============================================================================
# Object build statements for EXECUTABLE target testdemo


#############################################
# Order-only phony target for testdemo

build cmake_object_order_depends_target_testdemo: phony || .

build CMakeFiles/testdemo.dir/main.c.o: C_COMPILER__testdemo_unscanned_Debug /home/<USER>/testdemo/main.c || cmake_object_order_depends_target_testdemo
  DEP_FILE = CMakeFiles/testdemo.dir/main.c.o.d
  FLAGS = -g -fdiagnostics-color=always
  OBJECT_DIR = CMakeFiles/testdemo.dir
  OBJECT_FILE_DIR = CMakeFiles/testdemo.dir

build CMakeFiles/testdemo.dir/hardware.c.o: C_COMPILER__testdemo_unscanned_Debug /home/<USER>/testdemo/hardware.c || cmake_object_order_depends_target_testdemo
  DEP_FILE = CMakeFiles/testdemo.dir/hardware.c.o.d
  FLAGS = -g -fdiagnostics-color=always
  OBJECT_DIR = CMakeFiles/testdemo.dir
  OBJECT_FILE_DIR = CMakeFiles/testdemo.dir

build CMakeFiles/testdemo.dir/rs485.c.o: C_COMPILER__testdemo_unscanned_Debug /home/<USER>/testdemo/rs485.c || cmake_object_order_depends_target_testdemo
  DEP_FILE = CMakeFiles/testdemo.dir/rs485.c.o.d
  FLAGS = -g -fdiagnostics-color=always
  OBJECT_DIR = CMakeFiles/testdemo.dir
  OBJECT_FILE_DIR = CMakeFiles/testdemo.dir


# =============================================================================
# Link build statements for EXECUTABLE target testdemo


#############################################
# Link the executable testdemo

build testdemo: C_EXECUTABLE_LINKER__testdemo_Debug CMakeFiles/testdemo.dir/main.c.o CMakeFiles/testdemo.dir/hardware.c.o CMakeFiles/testdemo.dir/rs485.c.o
  FLAGS = -g
  LINK_LIBRARIES = -lusb-1.0  -lrockmong
  OBJECT_DIR = CMakeFiles/testdemo.dir
  POST_BUILD = :
  PRE_LINK = :
  TARGET_FILE = testdemo
  TARGET_PDB = testdemo.dbg


#############################################
# Utility command for edit_cache

build CMakeFiles/edit_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/testdemo/build/Desktop_Qt_6_9_1-Debug && /home/<USER>/Qt/Tools/CMake/bin/ccmake -S/home/<USER>/testdemo -B/home/<USER>/testdemo/build/Desktop_Qt_6_9_1-Debug
  DESC = Running CMake cache editor...
  pool = console
  restat = 1

build edit_cache: phony CMakeFiles/edit_cache.util


#############################################
# Utility command for rebuild_cache

build CMakeFiles/rebuild_cache.util: CUSTOM_COMMAND
  COMMAND = cd /home/<USER>/testdemo/build/Desktop_Qt_6_9_1-Debug && /home/<USER>/Qt/Tools/CMake/bin/cmake --regenerate-during-build -S/home/<USER>/testdemo -B/home/<USER>/testdemo/build/Desktop_Qt_6_9_1-Debug
  DESC = Running CMake to regenerate build system...
  pool = console
  restat = 1

build rebuild_cache: phony CMakeFiles/rebuild_cache.util


#############################################
# Utility command for list_install_components

build list_install_components: phony


#############################################
# Utility command for install

build CMakeFiles/install.util: CUSTOM_COMMAND all
  COMMAND = cd /home/<USER>/testdemo/build/Desktop_Qt_6_9_1-Debug && /home/<USER>/Qt/Tools/CMake/bin/cmake -P cmake_install.cmake
  DESC = Install the project...
  pool = console
  restat = 1

build install: phony CMakeFiles/install.util


#############################################
# Utility command for install/local

build CMakeFiles/install/local.util: CUSTOM_COMMAND all
  COMMAND = cd /home/<USER>/testdemo/build/Desktop_Qt_6_9_1-Debug && /home/<USER>/Qt/Tools/CMake/bin/cmake -DCMAKE_INSTALL_LOCAL_ONLY=1 -P cmake_install.cmake
  DESC = Installing only the local directory...
  pool = console
  restat = 1

build install/local: phony CMakeFiles/install/local.util


#############################################
# Utility command for install/strip

build CMakeFiles/install/strip.util: CUSTOM_COMMAND all
  COMMAND = cd /home/<USER>/testdemo/build/Desktop_Qt_6_9_1-Debug && /home/<USER>/Qt/Tools/CMake/bin/cmake -DCMAKE_INSTALL_DO_STRIP=1 -P cmake_install.cmake
  DESC = Installing the project stripped...
  pool = console
  restat = 1

build install/strip: phony CMakeFiles/install/strip.util

# =============================================================================
# Target aliases.

# =============================================================================
# Folder targets.

# =============================================================================

#############################################
# Folder: /home/<USER>/testdemo/build/Desktop_Qt_6_9_1-Debug

build all: phony testdemo

# =============================================================================
# Built-in targets


#############################################
# Re-run CMake if any of its inputs changed.

build build.ninja: RERUN_CMAKE | .qtc/package-manager/auto-setup.cmake .qtc/package-manager/maintenance_tool_provider.cmake /home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/CMakeCInformation.cmake /home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/CMakeCommonLanguageInclude.cmake /home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/CMakeGenericSystem.cmake /home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/CMakeInitializeConfigs.cmake /home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/CMakeLanguageInformation.cmake /home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/CMakeSystemSpecificInformation.cmake /home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/CMakeSystemSpecificInitialize.cmake /home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/Compiler/CMakeCommonCompilerMacros.cmake /home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/Compiler/GNU-C.cmake /home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/Compiler/GNU.cmake /home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/GNUInstallDirs.cmake /home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/Platform/Linux-GNU-C.cmake /home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/Platform/Linux-GNU.cmake /home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/Platform/Linux-Initialize.cmake /home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/Platform/Linux.cmake /home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/Platform/UnixPaths.cmake /home/<USER>/testdemo/CMakeLists.txt CMakeCache.txt CMakeFiles/3.30.5/CMakeCCompiler.cmake CMakeFiles/3.30.5/CMakeSystem.cmake
  pool = console


#############################################
# A missing CMake input file is not an error.

build .qtc/package-manager/auto-setup.cmake .qtc/package-manager/maintenance_tool_provider.cmake /home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/CMakeCInformation.cmake /home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/CMakeCommonLanguageInclude.cmake /home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/CMakeGenericSystem.cmake /home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/CMakeInitializeConfigs.cmake /home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/CMakeLanguageInformation.cmake /home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/CMakeSystemSpecificInformation.cmake /home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/CMakeSystemSpecificInitialize.cmake /home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/Compiler/CMakeCommonCompilerMacros.cmake /home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/Compiler/GNU-C.cmake /home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/Compiler/GNU.cmake /home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/GNUInstallDirs.cmake /home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/Platform/Linux-GNU-C.cmake /home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/Platform/Linux-GNU.cmake /home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/Platform/Linux-Initialize.cmake /home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/Platform/Linux.cmake /home/<USER>/Qt/Tools/CMake/share/cmake-3.30/Modules/Platform/UnixPaths.cmake /home/<USER>/testdemo/CMakeLists.txt CMakeCache.txt CMakeFiles/3.30.5/CMakeCCompiler.cmake CMakeFiles/3.30.5/CMakeSystem.cmake: phony


#############################################
# Clean all the built files.

build clean: CLEAN


#############################################
# Print all primary targets available.

build help: HELP


#############################################
# Make the all target the default.

default all
