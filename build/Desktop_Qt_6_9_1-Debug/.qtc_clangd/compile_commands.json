[{"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-std=gnu17", "-DQ_CREATOR_RUN", "-isystem", "/usr/local/include", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/20/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "/home/<USER>/testdemo/main.c"], "directory": "/home/<USER>/testdemo/build/Desktop_Qt_6_9_1-Debug/.qtc_clangd", "file": "/home/<USER>/testdemo/main.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-std=gnu17", "-DQ_CREATOR_RUN", "-isystem", "/usr/local/include", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/20/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "/home/<USER>/testdemo/hardware.c"], "directory": "/home/<USER>/testdemo/build/Desktop_Qt_6_9_1-Debug/.qtc_clangd", "file": "/home/<USER>/testdemo/hardware.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-std=gnu17", "-DQ_CREATOR_RUN", "-isystem", "/usr/local/include", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/20/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c", "/home/<USER>/testdemo/rs485.c"], "directory": "/home/<USER>/testdemo/build/Desktop_Qt_6_9_1-Debug/.qtc_clangd", "file": "/home/<USER>/testdemo/rs485.c"}, {"arguments": ["clang", "-Wno-documentation-unknown-command", "-Wno-unknown-warning-option", "-Wno-unknown-pragmas", "-nostdinc", "-nostdinc++", "-g", "-fdiagnostics-color=always", "-fsyntax-only", "-m64", "--target=x86_64-linux-gnu", "-std=gnu17", "-DQ_CREATOR_RUN", "-isystem", "/usr/local/include", "-isystem", "/home/<USER>/Qt/Tools/QtCreator/libexec/qtcreator/clang/lib/clang/20/include", "-isystem", "/usr/include/x86_64-linux-gnu", "-isystem", "/usr/include", "-fmessage-length=0", "-fdiagnostics-show-note-include-stack", "-fretain-comments-from-system-headers", "-fmacro-backtrace-limit=0", "-ferror-limit=1000", "-x", "c-header", "/home/<USER>/testdemo/appconfig.h"], "directory": "/home/<USER>/testdemo/build/Desktop_Qt_6_9_1-Debug/.qtc_clangd", "file": "/home/<USER>/testdemo/appconfig.h"}]