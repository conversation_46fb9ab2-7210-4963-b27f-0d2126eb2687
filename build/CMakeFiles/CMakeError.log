Performing C SOURCE FILE Test CMAKE_HAVE_LIBC_PTHREAD failed with the following output:
Change Dir: /app/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_f4098/fast && /usr/bin/make -f CMakeFiles/cmTC_f4098.dir/build.make CMakeFiles/cmTC_f4098.dir/build
make[1]: Entering directory '/app/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_f4098.dir/src.c.o
/usr/bin/cc   -Wall -Wextra -DCMAKE_HAVE_LIBC_PTHREAD   -std=gnu99 -o CMakeFiles/cmTC_f4098.dir/src.c.o   -c /app/build/CMakeFiles/CMakeTmp/src.c
Linking C executable cmTC_f4098
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_f4098.dir/link.txt --verbose=1
/usr/bin/cc  -Wall -Wextra -DCMAKE_HAVE_LIBC_PTHREAD    CMakeFiles/cmTC_f4098.dir/src.c.o  -o cmTC_f4098 
/usr/bin/ld: CMakeFiles/cmTC_f4098.dir/src.c.o: in function `main':
src.c:(.text+0x46): undefined reference to `pthread_create'
/usr/bin/ld: src.c:(.text+0x52): undefined reference to `pthread_detach'
/usr/bin/ld: src.c:(.text+0x63): undefined reference to `pthread_join'
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_f4098.dir/build.make:87: cmTC_f4098] Error 1
make[1]: Leaving directory '/app/build/CMakeFiles/CMakeTmp'
make: *** [Makefile:121: cmTC_f4098/fast] Error 2


Source file was:
#include <pthread.h>

void* test_func(void* data)
{
  return data;
}

int main(void)
{
  pthread_t thread;
  pthread_create(&thread, NULL, test_func, NULL);
  pthread_detach(thread);
  pthread_join(thread, NULL);
  pthread_atfork(NULL, NULL, NULL);
  pthread_exit(NULL);

  return 0;
}

Determining if the function pthread_create exists in the pthreads failed with the following output:
Change Dir: /app/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_05987/fast && /usr/bin/make -f CMakeFiles/cmTC_05987.dir/build.make CMakeFiles/cmTC_05987.dir/build
make[1]: Entering directory '/app/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_05987.dir/CheckFunctionExists.c.o
/usr/bin/cc   -Wall -Wextra -DCHECK_FUNCTION_EXISTS=pthread_create   -std=gnu99 -o CMakeFiles/cmTC_05987.dir/CheckFunctionExists.c.o   -c /usr/share/cmake-3.16/Modules/CheckFunctionExists.c
Linking C executable cmTC_05987
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_05987.dir/link.txt --verbose=1
/usr/bin/cc  -Wall -Wextra -DCHECK_FUNCTION_EXISTS=pthread_create    CMakeFiles/cmTC_05987.dir/CheckFunctionExists.c.o  -o cmTC_05987  -lpthreads 
/usr/bin/ld: cannot find -lpthreads
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_05987.dir/build.make:87: cmTC_05987] Error 1
make[1]: Leaving directory '/app/build/CMakeFiles/CMakeTmp'
make: *** [Makefile:121: cmTC_05987/fast] Error 2



