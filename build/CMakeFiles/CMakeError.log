Performing C SOURCE FILE Test CMAKE_HAVE_LIBC_PTHREAD failed with the following output:
Change Dir: /app/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_dd3c3/fast && /usr/bin/make -f CMakeFiles/cmTC_dd3c3.dir/build.make CMakeFiles/cmTC_dd3c3.dir/build
make[1]: Entering directory '/app/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_dd3c3.dir/src.c.o
/usr/bin/cc   -Wall -Wextra -DCMAKE_HAVE_LIBC_PTHREAD   -std=gnu99 -o CMakeFiles/cmTC_dd3c3.dir/src.c.o   -c /app/build/CMakeFiles/CMakeTmp/src.c
Linking C executable cmTC_dd3c3
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_dd3c3.dir/link.txt --verbose=1
/usr/bin/cc  -Wall -Wextra -DCMAKE_HAVE_LIBC_PTHREAD    CMakeFiles/cmTC_dd3c3.dir/src.c.o  -o cmTC_dd3c3 
/usr/bin/ld: CMakeFiles/cmTC_dd3c3.dir/src.c.o: in function `main':
src.c:(.text+0x46): undefined reference to `pthread_create'
/usr/bin/ld: src.c:(.text+0x52): undefined reference to `pthread_detach'
/usr/bin/ld: src.c:(.text+0x63): undefined reference to `pthread_join'
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_dd3c3.dir/build.make:87: cmTC_dd3c3] Error 1
make[1]: Leaving directory '/app/build/CMakeFiles/CMakeTmp'
make: *** [Makefile:121: cmTC_dd3c3/fast] Error 2


Source file was:
#include <pthread.h>

void* test_func(void* data)
{
  return data;
}

int main(void)
{
  pthread_t thread;
  pthread_create(&thread, NULL, test_func, NULL);
  pthread_detach(thread);
  pthread_join(thread, NULL);
  pthread_atfork(NULL, NULL, NULL);
  pthread_exit(NULL);

  return 0;
}

Determining if the function pthread_create exists in the pthreads failed with the following output:
Change Dir: /app/build/CMakeFiles/CMakeTmp

Run Build Command(s):/usr/bin/make cmTC_cc9ce/fast && /usr/bin/make -f CMakeFiles/cmTC_cc9ce.dir/build.make CMakeFiles/cmTC_cc9ce.dir/build
make[1]: Entering directory '/app/build/CMakeFiles/CMakeTmp'
Building C object CMakeFiles/cmTC_cc9ce.dir/CheckFunctionExists.c.o
/usr/bin/cc   -Wall -Wextra -DCHECK_FUNCTION_EXISTS=pthread_create   -std=gnu99 -o CMakeFiles/cmTC_cc9ce.dir/CheckFunctionExists.c.o   -c /usr/share/cmake-3.16/Modules/CheckFunctionExists.c
Linking C executable cmTC_cc9ce
/usr/bin/cmake -E cmake_link_script CMakeFiles/cmTC_cc9ce.dir/link.txt --verbose=1
/usr/bin/cc  -Wall -Wextra -DCHECK_FUNCTION_EXISTS=pthread_create    CMakeFiles/cmTC_cc9ce.dir/CheckFunctionExists.c.o  -o cmTC_cc9ce  -lpthreads 
/usr/bin/ld: cannot find -lpthreads
collect2: error: ld returned 1 exit status
make[1]: *** [CMakeFiles/cmTC_cc9ce.dir/build.make:87: cmTC_cc9ce] Error 1
make[1]: Leaving directory '/app/build/CMakeFiles/CMakeTmp'
make: *** [Makefile:121: cmTC_cc9ce/fast] Error 2



