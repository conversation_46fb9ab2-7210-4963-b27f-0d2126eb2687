# CMAKE generated file: DO NOT EDIT!
# Generated by "Unix Makefiles" Generator, CMake Version 3.16

# Delete rule output on recipe failure.
.DELETE_ON_ERROR:


#=============================================================================
# Special targets provided by cmake.

# Disable implicit rules so canonical targets will work.
.SUFFIXES:


# Remove some rules from gmake that .SUFFIXES does not remove.
SUFFIXES =

.SUFFIXES: .hpux_make_needs_suffix_list


# Suppress display of executed commands.
$(VERBOSE).SILENT:


# A target that is always out of date.
cmake_force:

.PHONY : cmake_force

#=============================================================================
# Set environment variables for the build.

# The shell in which to execute make rules.
SHELL = /bin/sh

# The CMake executable.
CMAKE_COMMAND = /usr/bin/cmake

# The command to remove a file.
RM = /usr/bin/cmake -E remove -f

# Escaping for special characters.
EQUALS = =

# The top-level source directory on which CMake was run.
CMAKE_SOURCE_DIR = /app

# The top-level build directory on which CMake was run.
CMAKE_BINARY_DIR = /app/build

# Include any dependencies generated for this target.
include CMakeFiles/testdemo.dir/depend.make

# Include the progress variables for this target.
include CMakeFiles/testdemo.dir/progress.make

# Include the compile flags for this target's objects.
include CMakeFiles/testdemo.dir/flags.make

CMakeFiles/testdemo.dir/main.c.o: CMakeFiles/testdemo.dir/flags.make
CMakeFiles/testdemo.dir/main.c.o: ../main.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/app/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_1) "Building C object CMakeFiles/testdemo.dir/main.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/testdemo.dir/main.c.o   -c /app/main.c

CMakeFiles/testdemo.dir/main.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/testdemo.dir/main.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /app/main.c > CMakeFiles/testdemo.dir/main.c.i

CMakeFiles/testdemo.dir/main.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/testdemo.dir/main.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /app/main.c -o CMakeFiles/testdemo.dir/main.c.s

CMakeFiles/testdemo.dir/hardware.c.o: CMakeFiles/testdemo.dir/flags.make
CMakeFiles/testdemo.dir/hardware.c.o: ../hardware.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/app/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_2) "Building C object CMakeFiles/testdemo.dir/hardware.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/testdemo.dir/hardware.c.o   -c /app/hardware.c

CMakeFiles/testdemo.dir/hardware.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/testdemo.dir/hardware.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /app/hardware.c > CMakeFiles/testdemo.dir/hardware.c.i

CMakeFiles/testdemo.dir/hardware.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/testdemo.dir/hardware.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /app/hardware.c -o CMakeFiles/testdemo.dir/hardware.c.s

CMakeFiles/testdemo.dir/rs485_flexible.c.o: CMakeFiles/testdemo.dir/flags.make
CMakeFiles/testdemo.dir/rs485_flexible.c.o: ../rs485_flexible.c
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --progress-dir=/app/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_3) "Building C object CMakeFiles/testdemo.dir/rs485_flexible.c.o"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -o CMakeFiles/testdemo.dir/rs485_flexible.c.o   -c /app/rs485_flexible.c

CMakeFiles/testdemo.dir/rs485_flexible.c.i: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Preprocessing C source to CMakeFiles/testdemo.dir/rs485_flexible.c.i"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -E /app/rs485_flexible.c > CMakeFiles/testdemo.dir/rs485_flexible.c.i

CMakeFiles/testdemo.dir/rs485_flexible.c.s: cmake_force
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green "Compiling C source to assembly CMakeFiles/testdemo.dir/rs485_flexible.c.s"
	/usr/bin/cc $(C_DEFINES) $(C_INCLUDES) $(C_FLAGS) -S /app/rs485_flexible.c -o CMakeFiles/testdemo.dir/rs485_flexible.c.s

# Object files for target testdemo
testdemo_OBJECTS = \
"CMakeFiles/testdemo.dir/main.c.o" \
"CMakeFiles/testdemo.dir/hardware.c.o" \
"CMakeFiles/testdemo.dir/rs485_flexible.c.o"

# External object files for target testdemo
testdemo_EXTERNAL_OBJECTS =

testdemo: CMakeFiles/testdemo.dir/main.c.o
testdemo: CMakeFiles/testdemo.dir/hardware.c.o
testdemo: CMakeFiles/testdemo.dir/rs485_flexible.c.o
testdemo: CMakeFiles/testdemo.dir/build.make
testdemo: CMakeFiles/testdemo.dir/link.txt
	@$(CMAKE_COMMAND) -E cmake_echo_color --switch=$(COLOR) --green --bold --progress-dir=/app/build/CMakeFiles --progress-num=$(CMAKE_PROGRESS_4) "Linking C executable testdemo"
	$(CMAKE_COMMAND) -E cmake_link_script CMakeFiles/testdemo.dir/link.txt --verbose=$(VERBOSE)

# Rule to build all files generated by this target.
CMakeFiles/testdemo.dir/build: testdemo

.PHONY : CMakeFiles/testdemo.dir/build

CMakeFiles/testdemo.dir/clean:
	$(CMAKE_COMMAND) -P CMakeFiles/testdemo.dir/cmake_clean.cmake
.PHONY : CMakeFiles/testdemo.dir/clean

CMakeFiles/testdemo.dir/depend:
	cd /app/build && $(CMAKE_COMMAND) -E cmake_depends "Unix Makefiles" /app /app /app/build /app/build /app/build/CMakeFiles/testdemo.dir/DependInfo.cmake --color=$(COLOR)
.PHONY : CMakeFiles/testdemo.dir/depend

