#!/bin/bash

echo "=== Docker Desktop for Mac 安装指南 ==="
echo ""
echo "请按照以下步骤安装Docker Desktop："
echo ""
echo "1. 打开浏览器访问："
echo "   https://www.docker.com/products/docker-desktop/"
echo ""
echo "2. 点击 'Download for Mac' 按钮"
echo ""
echo "3. 根据您的Mac芯片选择版本："
echo "   - Apple Silicon (M1/M2): 选择 'Mac with Apple chip'"
echo "   - Intel Mac: 选择 'Mac with Intel chip'"
echo ""
echo "4. 下载完成后，双击 Docker.dmg 文件"
echo ""
echo "5. 将Docker图标拖拽到Applications文件夹"
echo ""
echo "6. 从Applications文件夹启动Docker Desktop"
echo ""
echo "7. 首次启动时会要求输入管理员密码"
echo ""
echo "8. 等待Docker启动完成（状态栏会显示Docker图标）"
echo ""
echo "安装完成后，请重新运行此脚本来验证安装："
echo "bash install_docker.sh verify"
echo ""

if [ "$1" = "verify" ]; then
    echo "=== 验证Docker安装 ==="
    if command -v docker &> /dev/null; then
        echo "✅ Docker已安装"
        docker --version
        echo ""
        echo "测试Docker是否正常工作..."
        docker run hello-world
    else
        echo "❌ Docker未找到，请确保已正确安装Docker Desktop"
    fi
fi
