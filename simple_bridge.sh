#!/bin/bash

echo "=== 简单串口桥接（使用nc）==="
echo ""

USB_DEVICE="/dev/cu.usbserial-1410"
BRIDGE_PORT=9999

# 检查设备
if [ ! -e "$USB_DEVICE" ]; then
    echo "❌ USB设备不存在: $USB_DEVICE"
    echo "当前可用设备:"
    ls -la /dev/cu.*
    exit 1
fi

echo "✅ 找到USB设备: $USB_DEVICE"
echo "🌉 启动简单串口桥接..."
echo "端口: $BRIDGE_PORT"
echo ""
echo "按 Ctrl+C 停止桥接"
echo ""

# 使用nc创建简单的TCP到串口桥接
while true; do
    echo "等待连接..."
    nc -l $BRIDGE_PORT < "$USB_DEVICE" &
    NC_PID=$!
    
    # 等待连接
    sleep 1
    
    # 如果nc进程还在运行，等待它结束
    wait $NC_PID 2>/dev/null
    
    echo "连接断开，重新监听..."
    sleep 1
done
