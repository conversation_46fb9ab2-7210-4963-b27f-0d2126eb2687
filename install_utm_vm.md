# UTM虚拟机安装指南

## 方案2：使用UTM虚拟机（推荐用于完整Linux体验）

### 步骤1：安装UTM
1. 访问 https://mac.getutm.app/
2. 下载UTM for Mac
3. 安装UTM应用

### 步骤2：下载Ubuntu ISO
1. 访问 https://ubuntu.com/download/desktop
2. 下载Ubuntu 22.04 LTS Desktop版本
3. 保存ISO文件到Downloads文件夹

### 步骤3：创建虚拟机
1. 打开UTM应用
2. 点击"+"创建新虚拟机
3. 选择"虚拟化"
4. 选择"Linux"
5. 配置虚拟机：
   - 内存：4GB (4096MB)
   - CPU核心：2-4个
   - 存储：20GB
6. 选择Ubuntu ISO文件
7. 完成创建

### 步骤4：安装Ubuntu
1. 启动虚拟机
2. 按照Ubuntu安装向导完成安装
3. 重启后进入Ubuntu桌面

### 步骤5：配置开发环境
在Ubuntu虚拟机中运行：
```bash
# 更新系统
sudo apt update && sudo apt upgrade -y

# 安装开发工具
sudo apt install -y build-essential cmake gcc g++ make
sudo apt install -y libusb-1.0-0-dev pkg-config udev
sudo apt install -y git vim

# 复制项目文件到虚拟机
# 可以通过共享文件夹或USB传输
```

### 步骤6：USB设备直通
1. 在UTM中，选择虚拟机设置
2. 添加USB设备
3. 选择您的门禁控制器USB设备
4. 重启虚拟机

## 优缺点对比

### Docker容器方案
✅ 优点：
- 轻量级，启动快
- 资源占用少
- 易于管理和删除

❌ 缺点：
- USB设备支持可能有限
- 没有图形界面

### UTM虚拟机方案
✅ 优点：
- 完整的Linux环境
- 更好的USB设备支持
- 有图形界面，便于调试
- 更接近真实Linux环境

❌ 缺点：
- 资源占用较大
- 安装配置较复杂
- 启动较慢
