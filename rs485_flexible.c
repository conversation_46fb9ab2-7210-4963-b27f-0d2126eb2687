#include "appconfig.h"
#include <sys/stat.h>

int uart_fd = 0;
char buf[400];
unsigned char segCount = 0;
unsigned char lastSegCount = 0;
unsigned long lastTickCount = 0;

unsigned long GetTickCount() {
    struct timespec ts;
    clock_gettime(CLOCK_MONOTONIC, &ts); // 获取单调时间
    return (ts.tv_sec * 1000) + (ts.tv_nsec / 1000000);
}

void uart_sigio_handler(int sig) {
    int n = read(uart_fd, buf+32*segCount, sizeof(buf));

    if (32 == n) {
        //读入数据流，启动定时器检测和拼接下一帧
        segCount++;
        lastTickCount = GetTickCount();
        // 线程中判断32整数倍包长是否是最后一帧
    } else if (n > 0) {
        //最后一帧
        buf[n+32*segCount] = 0;
        uart_string_handler(buf, n+32*segCount);
        segCount = 0;
    }
}

// 尝试打开串口设备的函数
int try_open_serial_device(const char* device_path) {
    printf("尝试打开串口设备: %s\n", device_path);
    
    // 检查设备文件是否存在
    if (access(device_path, F_OK) != 0) {
        printf("设备文件不存在: %s\n", device_path);
        return -1;
    }
    
    // 尝试修改权限
    chmod(device_path, 0777);
    
    // 尝试打开设备
    int fd = open(device_path, O_RDWR | O_NOCTTY);
    if (fd < 0) {
        perror("无法打开设备");
        return -1;
    }
    
    printf("✅ 成功打开设备: %s (fd=%d)\n", device_path, fd);
    return fd;
}

void* uart_thread(void* arg) {
    const char* serial_devices[] = {
        "/dev/ttyUSB0",    // 标准USB转串口
        "/dev/ttyUSB1",    // 备用USB转串口
        "/dev/ttyACM0",    // USB CDC设备
        "/dev/ttyACM1",    // 备用USB CDC设备
        "/dev/ttyS0",      // 传统串口
        "/dev/ttyS1",      // 传统串口
        NULL
    };
    
    int fd = -1;
    const char* used_device = NULL;
    
    printf("🔍 搜索可用的串口设备...\n");
    
    // 尝试打开各种可能的串口设备
    for (int i = 0; serial_devices[i] != NULL; i++) {
        fd = try_open_serial_device(serial_devices[i]);
        if (fd >= 0) {
            used_device = serial_devices[i];
            break;
        }
    }
    
    if (fd < 0) {
        printf("❌ 未找到可用的串口设备\n");
        printf("💡 请检查：\n");
        printf("   1. USB转串口设备是否已连接\n");
        printf("   2. 设备驱动是否已安装\n");
        printf("   3. Docker容器是否有设备访问权限\n");
        printf("   4. 设备是否被其他程序占用\n");
        printf("\n");
        printf("🔍 当前可用的设备文件：\n");
        system("ls -la /dev/tty* | grep -E '(USB|ACM|S[0-9])' || echo '未找到串口设备'");
        
        // 进入测试模式
        printf("\n⚠️  进入测试模式（无硬件）\n");
        while(1) {
            sleep(10);
            printf("[测试模式] 串口线程运行中...\n");
        }
        return NULL;
    }

    printf("✅ 使用串口设备: %s\n", used_device);

    struct termios tty;
    tcgetattr(fd, &tty);
    cfsetispeed(&tty, B9600);  // 输入波特率9600
    cfsetospeed(&tty, B9600);  // 输出波特率9600
    tty.c_cflag &= ~PARENB;    // 无校验位
    tty.c_cflag &= ~CSTOPB;    // 1位停止位
    tty.c_cflag &= ~CSIZE;
    tty.c_cflag |= CS8;        // 8数据位
    tty.c_cc[VMIN] = 64;
    tty.c_cc[VTIME] = 200;
    tcsetattr(fd, TCSANOW, &tty);

    fcntl(fd, F_SETOWN, getpid());          // 绑定当前进程为信号接收者
    fcntl(fd, F_SETFL, O_ASYNC | O_NONBLOCK); // 启用异步通知
    signal(SIGIO, uart_sigio_handler);          // 注册信号处理函数

    uart_fd = fd;
    
    printf("✅ 串口配置完成，开始监听数据...\n");

    while(1) {
        usleep(50000);     //50ms 检查32整数倍的帧
        if (segCount > 0) {
            if (labs(lastTickCount - GetTickCount()) > 60) {
                //超时最后一帧
                buf[32*segCount] = 0;
                uart_string_handler(buf, 32*segCount);
                segCount = 0;
            }
        }
        //异常处理
    }
    
    const char *msg = "Finish Serial\n";
    write(fd, msg, strlen(msg));
    close(fd);
    return NULL;
}
