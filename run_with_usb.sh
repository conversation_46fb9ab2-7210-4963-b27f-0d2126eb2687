#!/bin/bash

echo "=== 带USB设备支持的门禁控制器运行脚本 ==="
echo ""

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker Desktop"
    exit 1
fi

# 检查Docker是否运行
if ! docker info &> /dev/null; then
    echo "❌ Docker未运行，请启动Docker Desktop"
    exit 1
fi

echo "✅ Docker已就绪"
echo ""

# 检查macOS上的USB串口设备
echo "🔍 检查主机上的USB串口设备..."
echo ""

# 查找所有可能的串口设备
SERIAL_DEVICES=()

# 检查常见的USB串口设备
for device in /dev/cu.usbserial* /dev/cu.usbmodem* /dev/cu.SLAB_USBtoUART* /dev/cu.wchusbserial* /dev/tty.usbserial* /dev/tty.usbmodem*; do
    if [ -e "$device" ]; then
        SERIAL_DEVICES+=("$device")
        echo "✅ 找到设备: $device"
    fi
done

# 如果没有找到设备，显示所有cu设备供参考
if [ ${#SERIAL_DEVICES[@]} -eq 0 ]; then
    echo "❌ 未找到常见的USB串口设备"
    echo ""
    echo "所有可用的串口设备："
    ls -la /dev/cu.* 2>/dev/null || echo "未找到任何cu设备"
    echo ""
    echo "所有可用的tty设备："
    ls -la /dev/tty.* 2>/dev/null | head -5
    echo ""
    echo "💡 请确保："
    echo "1. USB转串口设备已连接"
    echo "2. 已安装相应的驱动程序（如CP210x, FTDI, CH340等）"
    echo "3. 设备在系统中被正确识别"
    echo ""
    read -p "是否继续运行（将使用测试模式）？(y/n): " -n 1 -r
    echo ""
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
    DEVICE_MAPPING=""
else
    # 选择要使用的设备
    if [ ${#SERIAL_DEVICES[@]} -eq 1 ]; then
        SELECTED_DEVICE="${SERIAL_DEVICES[0]}"
        echo "✅ 自动选择设备: $SELECTED_DEVICE"
    else
        echo "发现多个串口设备，请选择："
        for i in "${!SERIAL_DEVICES[@]}"; do
            echo "$((i+1)). ${SERIAL_DEVICES[$i]}"
        done
        read -p "请输入设备编号 (1-${#SERIAL_DEVICES[@]}): " choice
        if [[ "$choice" =~ ^[0-9]+$ ]] && [ "$choice" -ge 1 ] && [ "$choice" -le "${#SERIAL_DEVICES[@]}" ]; then
            SELECTED_DEVICE="${SERIAL_DEVICES[$((choice-1))]}"
            echo "✅ 选择设备: $SELECTED_DEVICE"
        else
            echo "❌ 无效选择"
            exit 1
        fi
    fi
    
    # 设置设备映射
    DEVICE_MAPPING="--device=$SELECTED_DEVICE:/dev/ttyUSB0"
    echo "📱 设备映射: $SELECTED_DEVICE -> /dev/ttyUSB0"
fi

echo ""

# 构建Docker镜像（如果需要）
if ! docker image inspect access-controller &> /dev/null; then
    echo "🔨 构建Docker镜像..."
    docker build -t access-controller .
    if [ $? -ne 0 ]; then
        echo "❌ Docker镜像构建失败"
        exit 1
    fi
    echo "✅ Docker镜像构建成功"
fi

echo ""
echo "🚀 启动Linux容器（带USB设备支持）..."
echo "注意：容器启动后，您将进入Linux环境"
echo ""

# 运行容器并进入交互模式，映射USB设备
docker run -it --rm \
    --privileged \
    --name access_controller_usb \
    -v "$(pwd)":/app \
    $DEVICE_MAPPING \
    access-controller /bin/bash

echo ""
echo "容器已退出"
