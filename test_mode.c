#include "appconfig.h"

// 测试模式的硬件模拟函数
int test_SerialNumber = 12345;
int test_door_state = 0; // 0=关闭, 1=开启

// 模拟USB设备扫描
int UsbDevice_Scan(int* SerialNumbers) {
    printf("[测试模式] 模拟USB设备扫描\n");
    SerialNumbers[0] = test_SerialNumber;
    return 1; // 返回1个设备
}

// 模拟IO初始化
int IO_InitPin(int SerialNumber, int Pin, int Mode, int Pull) {
    printf("[测试模式] 初始化引脚 %d, 模式: %d, 上拉: %d\n", Pin, Mode, Pull);
    return 0; // 成功
}

// 模拟IO读取
int IO_ReadPin(int SerialNumber, int Pin, int *PinState) {
    static int button_pressed = 0;
    static int counter = 0;
    
    counter++;
    
    // 模拟按键状态变化
    if (Pin == 6) { // KEY_BLUE 手动开门按钮
        *PinState = (counter % 100 == 0) ? 0 : 1; // 每100次循环模拟按下一次
        if (*PinState == 0) {
            printf("[测试模式] 模拟按钮按下\n");
        }
    } else {
        *PinState = 1; // 其他引脚默认高电平
    }
    
    return 0; // 成功
}

// 模拟IO写入
int IO_WritePin(int SerialNumber, int Pin, int PinState) {
    if (Pin == 14) { // LED_RED 门锁控制
        test_door_state = PinState;
        printf("[测试模式] 门锁状态: %s\n", PinState ? "开启" : "关闭");
    } else if (Pin == 15) { // LED_GREEN
        printf("[测试模式] 绿灯状态: %s\n", PinState ? "开启" : "关闭");
    }
    return 0; // 成功
}

// 测试模式的串口线程
void* uart_thread_test(void* arg) {
    printf("[测试模式] 串口线程启动 - 模拟RS485通信\n");
    
    int counter = 0;
    while(1) {
        sleep(5); // 每5秒模拟一次数据接收
        counter++;
        
        // 模拟接收到读卡器数据
        char test_data[64];
        snprintf(test_data, sizeof(test_data), "[测试] 模拟卡号: %08d\n", counter);
        uart_string_handler(test_data, strlen(test_data));
        
        if (counter % 10 == 0) {
            printf("[测试模式] 串口通信正常，已模拟 %d 次数据接收\n", counter);
        }
    }
    
    return NULL;
}
