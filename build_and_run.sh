#!/bin/bash

echo "=== 门禁控制器编译和运行脚本（在Linux容器内使用）==="
echo ""

# 检查是否在容器内
if [ ! -f /.dockerenv ]; then
    echo "⚠️  此脚本应在Docker容器内运行"
    echo "请先运行: ./run_in_docker.sh"
    exit 1
fi

echo "✅ 在Linux容器环境中"
echo ""

# 检查库文件
echo "🔍 检查库文件..."
ls -la /usr/lib/libUSB2XXX.so 2>/dev/null && echo "✅ libUSB2XXX.so 已安装" || echo "❌ libUSB2XXX.so 未找到"
ls -la /usr/lib/librockmong.so 2>/dev/null && echo "✅ librockmong.so 已安装" || echo "❌ librockmong.so 未找到"
ls -la /usr/lib/libusb-1.0.so 2>/dev/null && echo "✅ libusb-1.0.so 已安装" || echo "❌ libusb-1.0.so 未找到"
echo ""

# 检查USB设备
echo "🔍 检查USB设备..."
lsusb 2>/dev/null || echo "lsusb命令不可用，安装usbutils..."
apt-get update && apt-get install -y usbutils
lsusb
echo ""

# 检查串口设备
echo "🔍 检查串口设备..."
ls -la /dev/ttyUSB* 2>/dev/null || echo "未找到 /dev/ttyUSB* 设备"
ls -la /dev/ttyACM* 2>/dev/null || echo "未找到 /dev/ttyACM* 设备"
echo ""

# 清理之前的构建
echo "🧹 清理之前的构建..."
rm -rf build

# 创建构建目录
echo "📁 创建构建目录..."
mkdir -p build
cd build

# 运行CMake配置
echo "⚙️  配置CMake..."
cmake ..

if [ $? -ne 0 ]; then
    echo "❌ CMake配置失败"
    exit 1
fi

# 编译项目
echo "🔨 编译项目..."
make

if [ $? -ne 0 ]; then
    echo "❌ 编译失败"
    exit 1
fi

echo "✅ 编译成功"
echo ""

# 检查可执行文件
if [ -f "./testdemo" ]; then
    echo "✅ 可执行文件已生成: ./testdemo"
    echo ""
    
    # 询问是否运行
    read -p "是否现在运行门禁控制器程序？(y/n): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🚀 启动门禁控制器..."
        echo "注意：程序需要连接USB设备才能正常工作"
        echo "按 Ctrl+C 停止程序"
        echo ""
        ./testdemo
    fi
else
    echo "❌ 可执行文件未生成"
fi
