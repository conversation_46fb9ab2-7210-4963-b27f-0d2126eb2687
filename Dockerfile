# 使用Ubuntu 20.04作为基础镜像
FROM ubuntu:20.04

# 设置非交互式安装
ENV DEBIAN_FRONTEND=noninteractive

# 更新包列表并安装必要的工具
RUN apt-get update && apt-get install -y \
    build-essential \
    cmake \
    gcc \
    g++ \
    make \
    libusb-1.0-0-dev \
    pkg-config \
    udev \
    vim \
    nano \
    && rm -rf /var/lib/apt/lists/*

# 创建工作目录
WORKDIR /app

# 复制项目文件
COPY . .

# 复制库文件到系统目录
RUN cp bsp/libs/*.so /usr/lib/ || true

# 设置库文件权限
RUN chmod 755 /usr/lib/libUSB2XXX.so || true
RUN chmod 755 /usr/lib/librockmong.so || true
RUN chmod 755 /usr/lib/libusb-1.0.so || true

# 更新动态链接库缓存
RUN ldconfig

# 创建构建目录
RUN mkdir -p build

# 暴露可能需要的端口（如果有网络功能）
EXPOSE 8080

# 设置启动命令
CMD ["/bin/bash"]
