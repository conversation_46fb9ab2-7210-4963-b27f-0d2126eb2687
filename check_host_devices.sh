#!/bin/bash

echo "=== 检查主机USB串口设备 ==="
echo ""

echo "🔍 检查macOS上的USB串口设备..."
echo ""

echo "1. 查找所有串口设备："
ls -la /dev/cu.* 2>/dev/null || echo "❌ 未找到 /dev/cu.* 设备"
echo ""

echo "2. 查找USB串口设备："
ls -la /dev/cu.usb* 2>/dev/null || echo "❌ 未找到 /dev/cu.usb* 设备"
echo ""

echo "3. 查找所有tty设备："
ls -la /dev/tty.* 2>/dev/null | head -10
echo ""

echo "4. 使用system_profiler查看USB设备："
system_profiler SPUSBDataType | grep -A 10 -B 5 -i "serial\|uart\|cp210\|ftdi\|ch340"
echo ""

echo "5. 查看所有USB设备："
system_profiler SPUSBDataType | grep -E "(Product ID|Vendor ID|Serial Number)" | head -20
echo ""

echo "💡 说明："
echo "- macOS上USB串口设备通常显示为 /dev/cu.usbserial-* 或 /dev/cu.usbmodem-*"
echo "- 需要将这些设备映射到Docker容器中的 /dev/ttyUSB0"
echo "- 可能需要安装USB转串口驱动（如CP210x, FTDI等）"
