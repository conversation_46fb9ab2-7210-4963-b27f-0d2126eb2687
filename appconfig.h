#ifndef APPCONFIG_H
#define APPCONFIG_H

#include <stdio.h>
#include <stdlib.h>
#include <unistd.h>
#include <termios.h>
#include <pthread.h>
#include <sys/msg.h>
#include <time.h>
#include <fcntl.h>
#include <signal.h>
#include <string.h>

extern pthread_t thread_keyboard;
extern pthread_t thread_uart;

extern void* keyboard_thread(void* arg);
extern void* uart_thread(void* arg);

extern void door_unlock();
extern int alarm_in();     // 1 输入报警  0无报警
extern int button_in();    // 0 按键按下  1按键未按下
extern int case_in();      // 1 防拆报警  0无报警

// extern void uart_sigio_handler(int sig);
extern void uart_string_handler(char* buf, int len);

typedef struct msgbuf {
    long mtype;       /* message type, must be > 0 */
    char mtext[1];    /* message data */
} msgbuff_t;

#endif // APPCONFIG_H
