cmake_minimum_required(VERSION 3.16)

project(testdemo LANGUAGES C)

# 设置C标准
set(CMAKE_C_STANDARD 99)

# 添加编译选项
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wall -Wextra")

# 查找pthread库
find_package(Threads REQUIRED)

# 添加选项来选择串口实现
option(USE_FLEXIBLE_SERIAL "Use flexible serial port detection" ON)

if(USE_FLEXIBLE_SERIAL)
    message(STATUS "使用灵活串口检测版本")
    add_executable(testdemo main.c
        appconfig.h
        hardware.c
        rs485_flexible.c)
else()
    message(STATUS "使用标准串口版本")
    add_executable(testdemo main.c
        appconfig.h
        hardware.c
        rs485.c)
endif()

target_link_libraries(${PROJECT_NAME}
    usb-1.0
    rockmong
    Threads::Threads
    rt
    m
)

include(GNUInstallDirs)
install(TARGETS testdemo
    LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
    RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
)
