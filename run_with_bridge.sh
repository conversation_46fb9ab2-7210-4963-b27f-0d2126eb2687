#!/bin/bash

echo "=== 使用串口桥接运行门禁控制器 ==="
echo ""

# 检查Docker是否运行
if ! docker info &> /dev/null; then
    echo "❌ Docker未运行，请启动Docker Desktop"
    exit 1
fi

echo "✅ Docker已就绪"
echo ""

# 获取主机IP（Docker内部访问主机）
HOST_IP="host.docker.internal"
BRIDGE_PORT=9999

echo "🌉 串口桥接配置:"
echo "主机IP: $HOST_IP"
echo "桥接端口: $BRIDGE_PORT"
echo ""

echo "💡 使用说明:"
echo "1. 在另一个终端窗口运行: ./setup_serial_bridge.sh"
echo "2. 等待桥接启动后，按任意键继续..."
read -n 1 -s

echo ""
echo "🚀 启动Docker容器（使用网络桥接）..."

# 启动容器，传递桥接信息
docker run -it --rm \
    --name access_controller_bridge \
    -v "$(pwd)":/app \
    -e SERIAL_BRIDGE_HOST="$HOST_IP" \
    -e SERIAL_BRIDGE_PORT="$BRIDGE_PORT" \
    access-controller /bin/bash
