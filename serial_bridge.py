#!/usr/bin/env python3
"""
串口桥接服务器 - 将USB串口设备桥接到网络端口
无需安装额外软件，使用Python标准库
"""

import socket
import serial
import threading
import time
import sys
import os

class SerialBridge:
    def __init__(self, serial_device, network_port=9999, baudrate=9600):
        self.serial_device = serial_device
        self.network_port = network_port
        self.baudrate = baudrate
        self.running = False
        self.serial_conn = None
        self.server_socket = None
        
    def start(self):
        """启动串口桥接服务"""
        print(f"=== 串口桥接服务器 ===")
        print(f"串口设备: {self.serial_device}")
        print(f"网络端口: {self.network_port}")
        print(f"波特率: {self.baudrate}")
        print("")
        
        # 检查串口设备
        if not os.path.exists(self.serial_device):
            print(f"❌ 串口设备不存在: {self.serial_device}")
            return False
            
        try:
            # 打开串口
            print("📱 打开串口设备...")
            self.serial_conn = serial.Serial(
                self.serial_device,
                baudrate=self.baudrate,
                timeout=1
            )
            print(f"✅ 串口已打开: {self.serial_conn.name}")
            
            # 创建网络服务器
            print("🌐 创建网络服务器...")
            self.server_socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.server_socket.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            self.server_socket.bind(('0.0.0.0', self.network_port))
            self.server_socket.listen(1)
            print(f"✅ 网络服务器已启动，监听端口: {self.network_port}")
            
            self.running = True
            print("")
            print("🌉 串口桥接服务已启动")
            print("等待客户端连接...")
            print("按 Ctrl+C 停止服务")
            print("")
            
            # 接受连接
            while self.running:
                try:
                    client_socket, client_addr = self.server_socket.accept()
                    print(f"✅ 客户端已连接: {client_addr}")
                    
                    # 为每个客户端创建处理线程
                    client_thread = threading.Thread(
                        target=self.handle_client,
                        args=(client_socket,)
                    )
                    client_thread.daemon = True
                    client_thread.start()
                    
                except socket.error as e:
                    if self.running:
                        print(f"❌ 网络错误: {e}")
                        
        except serial.SerialException as e:
            print(f"❌ 串口错误: {e}")
            return False
        except Exception as e:
            print(f"❌ 启动失败: {e}")
            return False
            
        return True
        
    def handle_client(self, client_socket):
        """处理客户端连接"""
        try:
            # 创建双向数据转发线程
            serial_to_network = threading.Thread(
                target=self.forward_serial_to_network,
                args=(client_socket,)
            )
            network_to_serial = threading.Thread(
                target=self.forward_network_to_serial,
                args=(client_socket,)
            )
            
            serial_to_network.daemon = True
            network_to_serial.daemon = True
            
            serial_to_network.start()
            network_to_serial.start()
            
            # 等待线程结束
            serial_to_network.join()
            network_to_serial.join()
            
        except Exception as e:
            print(f"❌ 客户端处理错误: {e}")
        finally:
            client_socket.close()
            print("🔌 客户端已断开")
            
    def forward_serial_to_network(self, client_socket):
        """将串口数据转发到网络"""
        while self.running:
            try:
                if self.serial_conn and self.serial_conn.in_waiting > 0:
                    data = self.serial_conn.read(self.serial_conn.in_waiting)
                    if data:
                        client_socket.send(data)
                        print(f"📤 串口->网络: {len(data)} 字节")
                time.sleep(0.01)
            except Exception as e:
                print(f"❌ 串口读取错误: {e}")
                break
                
    def forward_network_to_serial(self, client_socket):
        """将网络数据转发到串口"""
        while self.running:
            try:
                data = client_socket.recv(1024)
                if not data:
                    break
                if self.serial_conn:
                    self.serial_conn.write(data)
                    print(f"📥 网络->串口: {len(data)} 字节")
            except Exception as e:
                print(f"❌ 网络读取错误: {e}")
                break
                
    def stop(self):
        """停止桥接服务"""
        print("\n🛑 正在停止串口桥接服务...")
        self.running = False
        
        if self.server_socket:
            self.server_socket.close()
            
        if self.serial_conn:
            self.serial_conn.close()
            
        print("✅ 串口桥接服务已停止")

def main():
    # 默认配置
    serial_device = "/dev/cu.usbserial-1410"
    network_port = 9999
    baudrate = 9600
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        serial_device = sys.argv[1]
    if len(sys.argv) > 2:
        network_port = int(sys.argv[2])
    if len(sys.argv) > 3:
        baudrate = int(sys.argv[3])
        
    # 检查是否安装了pyserial
    try:
        import serial
    except ImportError:
        print("❌ 需要安装pyserial库")
        print("请运行: pip3 install pyserial")
        sys.exit(1)
        
    # 创建并启动桥接服务
    bridge = SerialBridge(serial_device, network_port, baudrate)
    
    try:
        if bridge.start():
            # 保持运行直到用户中断
            while True:
                time.sleep(1)
    except KeyboardInterrupt:
        pass
    finally:
        bridge.stop()

if __name__ == "__main__":
    main()
