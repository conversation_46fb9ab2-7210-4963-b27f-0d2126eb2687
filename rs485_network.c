#include "appconfig.h"
#include <sys/stat.h>
#include <sys/socket.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <netdb.h>

int uart_fd = 0;
char buf[400];
unsigned char segCount = 0;
unsigned char lastSegCount = 0;
unsigned long lastTickCount = 0;

unsigned long GetTickCount() {
    struct timespec ts;
    clock_gettime(CLOCK_MONOTONIC, &ts);
    return (ts.tv_sec * 1000) + (ts.tv_nsec / 1000000);
}

void uart_sigio_handler(int sig) {
    int n = read(uart_fd, buf+32*segCount, sizeof(buf));

    if (32 == n) {
        segCount++;
        lastTickCount = GetTickCount();
    } else if (n > 0) {
        buf[n+32*segCount] = 0;
        uart_string_handler(buf, n+32*segCount);
        segCount = 0;
    }
}

// 连接到串口桥接服务器
int connect_to_serial_bridge() {
    const char* host = getenv("SERIAL_BRIDGE_HOST");
    const char* port_str = getenv("SERIAL_BRIDGE_PORT");
    
    if (!host) host = "host.docker.internal";
    if (!port_str) port_str = "9999";
    
    int port = atoi(port_str);
    
    printf("🌉 尝试连接串口桥接服务器...\n");
    printf("主机: %s\n", host);
    printf("端口: %d\n", port);
    
    int sockfd = socket(AF_INET, SOCK_STREAM, 0);
    if (sockfd < 0) {
        perror("socket创建失败");
        return -1;
    }
    
    struct sockaddr_in server_addr;
    memset(&server_addr, 0, sizeof(server_addr));
    server_addr.sin_family = AF_INET;
    server_addr.sin_port = htons(port);
    
    // 解析主机名
    struct hostent *he = gethostbyname(host);
    if (he == NULL) {
        printf("❌ 无法解析主机名: %s\n", host);
        close(sockfd);
        return -1;
    }
    
    memcpy(&server_addr.sin_addr, he->h_addr_list[0], he->h_length);
    
    // 连接到服务器
    if (connect(sockfd, (struct sockaddr*)&server_addr, sizeof(server_addr)) < 0) {
        perror("连接串口桥接服务器失败");
        close(sockfd);
        return -1;
    }
    
    printf("✅ 成功连接到串口桥接服务器\n");
    return sockfd;
}

// 尝试打开串口设备的函数
int try_open_serial_device(const char* device_path) {
    printf("尝试打开串口设备: %s\n", device_path);
    
    if (access(device_path, F_OK) != 0) {
        printf("设备文件不存在: %s\n", device_path);
        return -1;
    }
    
    chmod(device_path, 0777);
    
    int fd = open(device_path, O_RDWR | O_NOCTTY);
    if (fd < 0) {
        perror("无法打开设备");
        return -1;
    }
    
    printf("✅ 成功打开设备: %s (fd=%d)\n", device_path, fd);
    return fd;
}

void* uart_thread(void* arg) {
    const char* serial_devices[] = {
        "/dev/ttyUSB0",
        "/dev/ttyUSB1", 
        "/dev/ttyACM0",
        "/dev/ttyACM1",
        "/dev/ttyS0",
        "/dev/ttyS1",
        NULL
    };
    
    int fd = -1;
    const char* used_device = NULL;
    
    printf("🔍 搜索可用的串口设备...\n");
    
    // 首先尝试网络桥接
    fd = connect_to_serial_bridge();
    if (fd >= 0) {
        used_device = "网络桥接";
        printf("✅ 使用串口桥接连接\n");
        goto configure_serial;
    }
    
    // 如果网络桥接失败，尝试本地设备
    printf("🔍 网络桥接失败，尝试本地串口设备...\n");
    for (int i = 0; serial_devices[i] != NULL; i++) {
        fd = try_open_serial_device(serial_devices[i]);
        if (fd >= 0) {
            used_device = serial_devices[i];
            break;
        }
    }
    
    if (fd < 0) {
        printf("❌ 未找到可用的串口设备或桥接\n");
        printf("💡 请检查：\n");
        printf("   1. 运行 ./setup_serial_bridge.sh 启动串口桥接\n");
        printf("   2. USB转串口设备是否已连接\n");
        printf("   3. Docker容器是否有网络访问权限\n");
        
        printf("\n⚠️  进入测试模式（无硬件）\n");
        while(1) {
            sleep(10);
            printf("[测试模式] 串口线程运行中...\n");
        }
        return NULL;
    }

configure_serial:
    printf("✅ 使用串口: %s\n", used_device);

    // 如果是真实串口设备，配置串口参数
    if (strstr(used_device, "/dev/tty") != NULL) {
        struct termios tty;
        tcgetattr(fd, &tty);
        cfsetispeed(&tty, B9600);
        cfsetospeed(&tty, B9600);
        tty.c_cflag &= ~PARENB;
        tty.c_cflag &= ~CSTOPB;
        tty.c_cflag &= ~CSIZE;
        tty.c_cflag |= CS8;
        tty.c_cc[VMIN] = 64;
        tty.c_cc[VTIME] = 200;
        tcsetattr(fd, TCSANOW, &tty);

        fcntl(fd, F_SETOWN, getpid());
        fcntl(fd, F_SETFL, O_ASYNC | O_NONBLOCK);
        signal(SIGIO, uart_sigio_handler);
    }

    uart_fd = fd;
    
    printf("✅ 串口配置完成，开始监听数据...\n");

    while(1) {
        usleep(50000);
        if (segCount > 0) {
            if (labs(lastTickCount - GetTickCount()) > 60) {
                buf[32*segCount] = 0;
                uart_string_handler(buf, 32*segCount);
                segCount = 0;
            }
        }
    }
    
    const char *msg = "Finish Serial\n";
    write(fd, msg, strlen(msg));
    close(fd);
    return NULL;
}
