cmake_minimum_required(VERSION 3.16)

project(testdemo LANGUAGES C)

# 设置C标准
set(CMAKE_C_STANDARD 99)

# 添加编译选项
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -Wall -Wextra")

# 查找pthread库
find_package(Threads REQUIRED)

# 添加测试模式编译选项
option(TEST_MODE "Enable test mode without hardware" OFF)

if(TEST_MODE)
    message(STATUS "编译测试模式版本")
    add_definitions(-DTEST_MODE)
    
    add_executable(testdemo_test 
        main.c
        appconfig.h
        test_mode.c
        rs485.c)
    
    target_link_libraries(testdemo_test
        Threads::Threads
        rt
        m
    )
    
    # 创建符号链接，方便运行
    add_custom_command(TARGET testdemo_test POST_BUILD
        COMMAND ${CMAKE_COMMAND} -E create_symlink testdemo_test testdemo
        COMMENT "创建testdemo符号链接")
        
else()
    message(STATUS "编译正常模式版本")
    
    add_executable(testdemo main.c
        appconfig.h
        hardware.c
        rs485.c)
    
    target_link_libraries(testdemo
        usb-1.0
        rockmong
        Threads::Threads
        rt
        m
    )
endif()

include(GNUInstallDirs)
if(TEST_MODE)
    install(TARGETS testdemo_test
        LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
        RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
    )
else()
    install(TARGETS testdemo
        LIBRARY DESTINATION ${CMAKE_INSTALL_LIBDIR}
        RUNTIME DESTINATION ${CMAKE_INSTALL_BINDIR}
    )
endif()
