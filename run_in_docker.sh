#!/bin/bash

echo "=== 门禁控制器 Docker 运行脚本 ==="
echo ""

# 检查Docker是否安装
if ! command -v docker &> /dev/null; then
    echo "❌ Docker未安装，请先安装Docker Desktop"
    echo "运行: ./install_docker.sh 查看安装指南"
    exit 1
fi

# 检查Docker是否运行
if ! docker info &> /dev/null; then
    echo "❌ Docker未运行，请启动Docker Desktop"
    exit 1
fi

echo "✅ Docker已就绪"
echo ""

# 构建Docker镜像
echo "🔨 构建Docker镜像..."
docker build -t access-controller .

if [ $? -ne 0 ]; then
    echo "❌ Docker镜像构建失败"
    exit 1
fi

echo "✅ Docker镜像构建成功"
echo ""

# 运行容器
echo "🚀 启动Linux容器..."
echo "注意：容器启动后，您将进入Linux环境"
echo ""

# 运行容器并进入交互模式
docker run -it --rm \
    --privileged \
    --name access_controller \
    -v "$(pwd)":/app \
    -v /dev:/dev \
    access-controller /bin/bash

echo ""
echo "容器已退出"
