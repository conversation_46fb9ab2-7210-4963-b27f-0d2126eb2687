#!/bin/bash

echo "=== 在macOS上直接运行门禁控制器 ==="
echo ""

# 检查是否有cmake
if ! command -v cmake &> /dev/null; then
    echo "❌ 需要安装cmake"
    echo "请运行: brew install cmake"
    echo "或者从 https://cmake.org/download/ 下载安装"
    exit 1
fi

echo "✅ cmake已安装: $(cmake --version | head -1)"

# 检查USB设备
USB_DEVICE="/dev/cu.usbserial-1410"
if [ -e "$USB_DEVICE" ]; then
    echo "✅ 找到USB设备: $USB_DEVICE"
    ls -la "$USB_DEVICE"
else
    echo "⚠️  USB设备未找到: $USB_DEVICE"
    echo "当前可用设备:"
    ls -la /dev/cu.* 2>/dev/null || echo "未找到任何cu设备"
    echo ""
    echo "程序将在测试模式下运行"
fi

echo ""

# 创建macOS构建目录
echo "📁 创建macOS构建目录..."
rm -rf build_macos
mkdir -p build_macos
cd build_macos

# 使用macOS版本的CMakeLists.txt
echo "⚙️  配置CMake（macOS版本）..."
cmake -f ../CMakeLists_macos.txt ..

if [ $? -ne 0 ]; then
    echo "❌ CMake配置失败"
    exit 1
fi

# 编译项目
echo "🔨 编译项目..."
make

if [ $? -ne 0 ]; then
    echo "❌ 编译失败"
    exit 1
fi

echo "✅ 编译成功"
echo ""

# 检查可执行文件
if [ -f "./testdemo_macos" ]; then
    echo "✅ 可执行文件已生成: ./testdemo_macos"
    echo ""
    
    # 询问是否运行
    read -p "是否现在运行门禁控制器程序？(y/n): " -n 1 -r
    echo ""
    
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🚀 启动门禁控制器（macOS版本）..."
        echo "注意：程序会尝试连接USB设备，如果失败会进入测试模式"
        echo "按 Ctrl+C 停止程序"
        echo ""
        ./testdemo_macos
    fi
else
    echo "❌ 可执行文件未生成"
fi
