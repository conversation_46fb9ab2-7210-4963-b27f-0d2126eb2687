#!/bin/bash

echo "=== Python串口桥接设置 ==="
echo ""

# 检查Python3
if ! command -v python3 &> /dev/null; then
    echo "❌ 需要Python3，请先安装Python"
    exit 1
fi

echo "✅ Python3已安装: $(python3 --version)"

# 检查是否安装了pyserial
echo "📦 检查pyserial库..."
if python3 -c "import serial" 2>/dev/null; then
    echo "✅ pyserial已安装"
else
    echo "📦 安装pyserial库..."
    pip3 install pyserial
    if [ $? -ne 0 ]; then
        echo "❌ pyserial安装失败"
        echo "请手动运行: pip3 install pyserial"
        exit 1
    fi
    echo "✅ pyserial安装成功"
fi

USB_DEVICE="/dev/cu.usbserial-1410"
BRIDGE_PORT=9999

if [ ! -e "$USB_DEVICE" ]; then
    echo "❌ USB设备 $USB_DEVICE 不存在"
    echo "当前可用设备:"
    ls -la /dev/cu.*
    exit 1
fi

echo ""
echo "🌉 启动Python串口桥接..."
echo "USB设备: $USB_DEVICE"
echo "网络端口: $BRIDGE_PORT"
echo ""
echo "按 Ctrl+C 停止桥接"
echo ""

# 启动Python串口桥接
python3 serial_bridge.py "$USB_DEVICE" "$BRIDGE_PORT"
