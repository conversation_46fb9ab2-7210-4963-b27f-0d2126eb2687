#!/bin/bash

echo "=== 设备检查脚本 ==="
echo ""

echo "🔍 检查串口设备..."
echo "查找 /dev/ttyUSB* 设备："
ls -la /dev/ttyUSB* 2>/dev/null || echo "❌ 未找到 /dev/ttyUSB* 设备"

echo ""
echo "查找 /dev/ttyACM* 设备："
ls -la /dev/ttyACM* 2>/dev/null || echo "❌ 未找到 /dev/ttyACM* 设备"

echo ""
echo "查找所有串口相关设备："
ls -la /dev/tty* | grep -E "(USB|ACM|S[0-9])" || echo "❌ 未找到串口设备"

echo ""
echo "🔍 检查USB设备..."
lsusb 2>/dev/null || echo "❌ lsusb命令不可用"

echo ""
echo "🔍 检查所有设备文件..."
ls -la /dev/ | grep -E "(tty|usb)" | head -10

echo ""
echo "🔍 检查内核模块..."
lsmod | grep -E "(usb|serial|ftdi|cp210x)" || echo "❌ 未找到相关内核模块"

echo ""
echo "🔍 检查dmesg日志（USB相关）..."
dmesg | grep -i usb | tail -5 || echo "❌ 无USB相关日志"

echo ""
echo "💡 建议："
echo "1. 确保USB设备已连接到主机"
echo "2. 在Docker运行时添加设备映射"
echo "3. 检查设备权限"
echo "4. 可能需要安装USB转串口驱动"
