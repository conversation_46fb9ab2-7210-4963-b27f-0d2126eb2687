#!/bin/bash

echo "=== macOS串口桥接解决方案 ==="
echo ""

USB_DEVICE="/dev/cu.usbserial-1410"

# 检查设备
if [ ! -e "$USB_DEVICE" ]; then
    echo "❌ USB设备不存在: $USB_DEVICE"
    echo "当前可用设备:"
    ls -la /dev/cu.*
    exit 1
fi

echo "✅ 找到USB设备: $USB_DEVICE"
echo ""

# 方案1：检查是否有Python
if command -v python3 &> /dev/null; then
    echo "🐍 方案1：使用Python串口桥接"
    echo "安装pyserial库..."
    
    # 尝试安装pyserial
    python3 -m pip install pyserial --user 2>/dev/null || {
        echo "❌ 无法安装pyserial，尝试其他方案..."
    }
    
    # 检查是否安装成功
    if python3 -c "import serial" 2>/dev/null; then
        echo "✅ pyserial已安装，启动Python桥接..."
        python3 serial_bridge.py "$USB_DEVICE" 9999
        exit 0
    fi
fi

# 方案2：使用screen命令
echo "📺 方案2：使用screen命令"
echo "这将直接连接到串口设备进行测试"
echo ""
echo "使用说明："
echo "1. 连接后可以直接发送/接收数据"
echo "2. 按 Ctrl+A 然后按 K 退出"
echo "3. 或者按 Ctrl+A 然后按 D 分离会话"
echo ""
read -p "按回车键继续，或按 Ctrl+C 取消..."

# 使用screen连接串口
screen "$USB_DEVICE" 9600
