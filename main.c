#include <stdio.h>
#include "appconfig.h"

pthread_t thread_keyboard;
pthread_t thread_uart;

void uart_string_handler(char* buf, int len)
{
    printf("%s", buf);
    fflush(stdout);
}

int main()
{
    int err;

    printf("Access manager !\n");

    err = pthread_create(&thread_keyboard, NULL, keyboard_thread, NULL);
    err = pthread_create(&thread_uart, NULL, uart_thread, NULL);

    while(1)
    {
      sleep(1);
      // 处理网络协议;
    }

    return 0;
}
