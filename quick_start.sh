#!/bin/bash

echo "=== 快速启动门禁控制器（带USB设备）==="
echo ""

# 检查找到的USB设备
USB_DEVICE="/dev/cu.usbserial-1410"

if [ -e "$USB_DEVICE" ]; then
    echo "✅ 找到USB串口设备: $USB_DEVICE"
    echo "📱 设备权限:"
    ls -la "$USB_DEVICE"
    echo ""
    
    echo "🚀 启动Docker容器（映射USB设备）..."
    echo "设备映射: $USB_DEVICE -> /dev/ttyUSB0"
    echo ""
    
    # 启动容器
    docker run -it --rm \
        --privileged \
        --name access_controller_usb \
        -v "$(pwd)":/app \
        --device="$USB_DEVICE:/dev/ttyUSB0" \
        access-controller /bin/bash
        
else
    echo "❌ USB设备 $USB_DEVICE 不存在"
    echo "请检查设备连接或运行: ls -la /dev/cu.*"
fi
