#!/bin/bash

echo "=== 设置串口桥接（macOS到Docker）==="
echo ""

# 检查是否安装了socat
if ! command -v socat &> /dev/null; then
    echo "📦 安装socat..."
    if command -v brew &> /dev/null; then
        brew install socat
    else
        echo "❌ 请先安装Homebrew，然后运行: brew install socat"
        echo "Homebrew安装: /bin/bash -c \"\$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)\""
        exit 1
    fi
fi

USB_DEVICE="/dev/cu.usbserial-1410"
BRIDGE_PORT=9999

if [ ! -e "$USB_DEVICE" ]; then
    echo "❌ USB设备 $USB_DEVICE 不存在"
    echo "当前可用设备:"
    ls -la /dev/cu.*
    exit 1
fi

echo "✅ 找到USB设备: $USB_DEVICE"
echo ""

# 启动串口桥接
echo "🌉 启动串口桥接..."
echo "USB设备: $USB_DEVICE"
echo "网络端口: $BRIDGE_PORT"
echo ""
echo "按 Ctrl+C 停止桥接"
echo ""

# 使用socat将串口设备桥接到网络端口
socat TCP-LISTEN:$BRIDGE_PORT,reuseaddr,fork FILE:$USB_DEVICE,b9600,raw,echo=0
